# Auspex Records Website

A modern website for Auspex Records, showcasing psychedelic trance music releases and live performances. Built with React, TypeScript, and deployed on AWS with separate staging and production environments.

## Project Structure

```
auspex-website/
├── src/                    # Frontend React application
│   ├── components/         # Reusable React components
│   ├── pages/             # Page components
│   ├── types/             # TypeScript type definitions
│   └── utils/             # API utilities and helpers
├── terraform/             # Infrastructure as Code
│   ├── environments/      # Environment-specific configurations
│   │   ├── staging/       # Staging environment (stage.auspexrecords.com)
│   │   └── prod/          # Production environment (auspexrecords.com)
│   ├── modules/           # Reusable Terraform modules
│   │   ├── api/           # API Gateway and Lambda resources
│   │   ├── database/      # DynamoDB resources
│   │   ├── domains/       # Route53 and ACM resources
│   │   └── website/       # S3 and CloudFront resources
│   ├── bootstrap/         # Bootstrap resources for Terraform state
│   └── seed-data.sh       # Script to seed data into DynamoDB
├── lambda/                # Lambda function code
│   ├── index.ts           # API handler
│   ├── package.json       # Lambda dependencies
│   └── tsconfig.json      # TypeScript configuration
└── dist/                  # Built frontend assets (generated)
```

## Technology Stack

### Frontend

- React with TypeScript
- React Router for navigation
- Tailwind CSS for styling
- YouTube video embedding

### Backend

- AWS S3 for static website hosting
- CloudFront for content delivery
- API Gateway + Lambda for serverless API
- DynamoDB for data storage
- Route53 for DNS management
- ACM for SSL/TLS certificates
- Terraform for infrastructure management

## Development Setup

1. Install dependencies:

   ```bash
   # Install frontend dependencies
   npm install

   # Install Lambda dependencies
   cd lambda
   npm install
   ```

2. Set up environment variables:

   ```bash
   # Create .env file in the project root for development:
   VITE_API_URL=https://ma1l0omu6k.execute-api.us-west-1.amazonaws.com  # Staging API
   ```

3. Start the development server:

   ```bash
   npm run dev
   ```

## Deployment

The project uses automated deployment scripts for both environments:

### Deploy to Staging
```bash
npm run deploy:staging
```

### Deploy to Production
```bash
npm run deploy:prod
```

### Manual Build (if needed)
```bash
# Build for staging
npm run build:staging

# Build for production
npm run build:prod

# Generic build (uses .env file)
npm run build
```

## Infrastructure Deployment

### Prerequisites

1. Install Terraform:
   ```bash
   brew install terraform    # macOS
   # or use your system's package manager
   ```

2. Configure AWS credentials:
   ```bash
   aws configure
   # Enter your AWS access key, secret key, and default region
   ```

### Bootstrap (One-time setup)

Create the S3 bucket and DynamoDB table for Terraform state:

```bash
cd terraform/bootstrap
terraform init
terraform apply
```

### Deploy Staging Environment

```bash
cd terraform/environments/staging
terraform init
terraform plan    # Review the changes
terraform apply   # Deploy the infrastructure
```

### Deploy Production Environment

```bash
cd terraform/environments/prod
terraform init
terraform plan    # Review the changes
terraform apply   # Deploy the infrastructure
```

### Seed Database

```bash
cd terraform
./seed-data.sh staging  # Seed staging environment
./seed-data.sh prod     # Seed production environment
```

### Deploy Frontend

Use the automated deployment scripts (recommended):
```bash
npm run deploy:staging  # Deploy to staging
npm run deploy:prod     # Deploy to production
```

## Environments

| Environment | URL | Purpose | Deploy Command |
|-------------|-----|---------|----------------|
| **Staging** | https://stage.auspexrecords.com | Testing and development | `npm run deploy:staging` |
| **Production** | https://auspexrecords.com | Live website | `npm run deploy:prod` |

## Features

- Browse music releases with embedded YouTube videos
- Watch live performance recordings
- Download tracks directly
- Access music on various platforms (Bandcamp, SoundCloud, Spotify, etc.)
- Responsive design for all devices
- Dark theme optimized for music content

## Current Releases

1. Reflections EP by Oak Project

   - 5 tracks including Nature, For the wall climbers, Cleaning Energy, etc.

2. The Pots Of My Heart, Are Full Of Your Seeds by Paranoiac

   - 6 tracks including Its all how you look at it, Lentamente, Creo en el amor, etc.

3. Time Crystal by Maru Secrets

   - 2 tracks: The Missing Crystal and The Land Before Time

4. Ion Tentacles by Aeromancer

   - 9 tracks including What happened to you, Ion Tentacles, Palm Groove, etc.

5. Midnight Sanctuary by Caixedia Camista
   - 3 tracks: Qbit, End of Time, and See The Light

## Contributing

1. Create a feature branch
2. Make your changes
3. Submit a pull request

## License

All rights reserved © Auspex Records
