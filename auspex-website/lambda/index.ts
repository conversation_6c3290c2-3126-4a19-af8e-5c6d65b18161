import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand, ScanCommand } from "@aws-sdk/lib-dynamodb";
import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";

const client = new DynamoDBClient({});
const dynamodb = DynamoDBDocumentClient.from(client);
const RELEASES_TABLE = process.env.RELEASES_TABLE!;
const PERFORMANCES_TABLE = process.env.PERFORMANCES_TABLE!;

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { httpMethod, resource } = event;

    // CORS headers
    const headers = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Content-Type",
      "Access-Control-Allow-Methods": "OPTIONS,GET,POST",
    };

    // Handle releases
    if (resource === "/releases") {
      if (httpMethod === "GET") {
        const result = await dynamodb.send(
          new ScanCommand({
            TableName: RELEASES_TABLE,
          }),
        );

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(result.Items),
        };
      }

      if (httpMethod === "POST") {
        const release = JSON.parse(event.body || "{}");
        await dynamodb.send(
          new PutCommand({
            TableName: RELEASES_TABLE,
            Item: release,
          }),
        );

        return {
          statusCode: 201,
          headers,
          body: JSON.stringify(release),
        };
      }
    }

    // Handle performances
    if (resource === "/performances") {
      if (httpMethod === "GET") {
        const result = await dynamodb.send(
          new ScanCommand({
            TableName: PERFORMANCES_TABLE,
          }),
        );

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(result.Items),
        };
      }

      if (httpMethod === "POST") {
        const performance = JSON.parse(event.body || "{}");
        await dynamodb.send(
          new PutCommand({
            TableName: PERFORMANCES_TABLE,
            Item: performance,
          }),
        );

        return {
          statusCode: 201,
          headers,
          body: JSON.stringify(performance),
        };
      }
    }

    // Handle OPTIONS requests (CORS preflight)
    if (httpMethod === "OPTIONS") {
      return {
        statusCode: 200,
        headers,
        body: "",
      };
    }

    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ message: "Not Found" }),
    };
  } catch (error) {
    console.error("Error:", error);
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ message: "Internal Server Error" }),
    };
  }
};
