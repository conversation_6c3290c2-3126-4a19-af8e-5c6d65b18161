{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "lib": ["es2020"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "outDir": "dist", "esModuleInterop": true}, "exclude": ["node_modules", "dist"], "include": ["index.ts"]}