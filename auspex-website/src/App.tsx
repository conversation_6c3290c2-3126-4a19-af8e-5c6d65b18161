import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
import { Navigation } from "./components/Navigation";
import { HomePage } from "./pages/HomePage";
import { LivePerformancesPage } from "./pages/LivePerformancesPage";

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-black">
        <Navigation />
        <main>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/live" element={<LivePerformancesPage />} />
          </Routes>
        </main>

        {/* Footer */}
        <footer className="bg-gray-900 py-8 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col items-center space-y-4">
              <img
                src="/assets/logos/White_transparent_background.png"
                alt="Auspex Records"
                className="h-16"
              />
              <div className="flex space-x-6">
                <a
                  href="https://auspexrecords.bandcamp.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Bandcamp
                </a>
                <a
                  href="https://www.youtube.com/@AuspexRecords"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  YouTube
                </a>
                <a
                  href="https://www.instagram.com/auspex_records"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Instagram
                </a>
                <a
                  href="https://soundcloud.com/auspexrecords"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  SoundCloud
                </a>
              </div>
              <p className="text-gray-400 text-sm">
                © {new Date().getFullYear()} Auspex Records. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </Router>
  );
}

export default App;
