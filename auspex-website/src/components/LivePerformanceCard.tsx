import type { LivePerformance } from "../types";
import { YouTubeEmbed } from "./YouTubeEmbed";

interface LivePerformanceCardProps {
  performance: LivePerformance;
}

export const LivePerformanceCard = ({ performance }: LivePerformanceCardProps) => {
  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden shadow-xl">
      <div className="p-6">
        <div className="space-y-4">
          {/* Video Embed */}
          <YouTubeEmbed videoId={performance.youtubeVideoId} title={performance.title} />

          {/* Performance Details */}
          <div className="mt-4">
            <h2 className="text-2xl font-bold text-white">{performance.title}</h2>
            <h3 className="text-xl text-gray-300 mb-2">{performance.artist}</h3>
            <p className="text-gray-400 mb-4">
              {new Date(performance.date + "T00:00:00").toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
            <p className="text-gray-300 leading-relaxed">{performance.description}</p>
          </div>

          {/* YouTube Link */}
          <div className="mt-4">
            <a
              href={`https://www.youtube.com/watch?v=${performance.youtubeVideoId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-red-600 text-white px-4 py-2 rounded-full text-sm hover:bg-red-700 transition-colors"
            >
              Watch on YouTube
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
