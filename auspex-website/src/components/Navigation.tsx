import { Link, useLocation } from "react-router-dom";

export const Navigation = () => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-32 py-6">
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center">
              <img
                src="/assets/logos/Auspex_white_ink_transparent_background.svg"
                alt="Auspex Records"
                className="h-24 md:h-28"
                style={{ filter: 'brightness(1.2) contrast(1.1)' }}
              />
            </Link>
          </div>
          <div className="flex space-x-8">
            <Link
              to="/"
              className={`${
                isActive("/") ? "text-orange-500" : "hover:text-orange-400"
              } transition-colors duration-200 text-xl font-medium`}
            >
              Releases
            </Link>
            <Link
              to="/live"
              className={`${
                isActive("/live") ? "text-orange-500" : "hover:text-orange-400"
              } transition-colors duration-200 text-xl font-medium`}
            >
              Live Performances
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};
