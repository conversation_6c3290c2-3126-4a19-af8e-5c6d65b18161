import { useEffect, useState, useMemo } from "react";
import { ReleaseCard } from "../components/ReleaseCard";
import type { Release } from "../types";
import { api } from "../utils/api";

export const HomePage = () => {
  const [releases, setReleases] = useState<Release[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<string | null | undefined>(undefined);

  useEffect(() => {
    const fetchReleases = async () => {
      try {
        const data = await api.fetchReleases();
        setReleases(data);
        setLoading(false);
      } catch (err) {
        setError("Failed to load releases");
        setLoading(false);
      }
    };

    fetchReleases();
  }, []);

  // Extract unique years from release dates
  const availableYears = useMemo(() => {
    if (!releases.length) return [];

    const years = releases.map(release => {
      const date = new Date(release.releaseDate);
      return date.getFullYear().toString();
    });

    // Get unique years and sort in descending order (newest first)
    return [...new Set(years)].sort((a, b) => parseInt(b) - parseInt(a));
  }, [releases]);

  // Set the default selected year to the most recent year only on initial load
  useEffect(() => {
    if (availableYears.length > 0 && selectedYear === undefined) {
      setSelectedYear(availableYears[0]);
    }
  }, [availableYears, selectedYear]);

  // Filter releases by selected year and sort by release date
  const filteredReleases = useMemo(() => {
    let result = [...releases];

    // Filter by year if a year is selected
    if (selectedYear) {
      result = result.filter(release => {
        const releaseYear = new Date(release.releaseDate).getFullYear().toString();
        return releaseYear === selectedYear;
      });
    }

    // Sort by release date (newest to oldest)
    result.sort((a, b) => {
      const dateA = new Date(a.releaseDate);
      const dateB = new Date(b.releaseDate);
      return dateB.getTime() - dateA.getTime();
    });

    return result;
  }, [releases, selectedYear]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-white">Loading releases...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-4xl font-bold text-white mb-8 text-center lg:text-left">Releases</h1>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Year navigation sidebar - Now on the left */}
        <div className="lg:col-span-3 order-first">
          <div className="bg-gray-900 rounded-lg p-6 sticky top-4">
            <h3 className="text-xl font-semibold text-white mb-4 border-b border-gray-700 pb-3 text-center">
              Browse by Year
            </h3>
            <div className="space-y-3">
              {availableYears.map(year => (
                <button
                  key={year}
                  onClick={() => setSelectedYear(year)}
                  className={`block w-full text-left px-4 py-3 rounded-md transition-colors ${
                    selectedYear === year
                      ? "bg-orange-500 text-white font-medium"
                      : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white"
                  }`}
                >
                  {year}
                </button>
              ))}
              {availableYears.length > 1 && (
                <button
                  onClick={() => setSelectedYear(null)}
                  className={`block w-full text-left px-4 py-3 rounded-md transition-colors ${
                    selectedYear === null
                      ? "bg-orange-500 text-white font-medium"
                      : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white"
                  }`}
                >
                  All Years
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Main content - Release cards - Now on the right */}
        <div className="lg:col-span-9 order-last">
          <div className="bg-gray-900 rounded-lg p-6 lg:pr-10">
            <h2 className="text-2xl font-semibold text-orange-500 mb-6 border-b border-gray-800 pb-3">
              {selectedYear === null ? "All Years" : selectedYear}
            </h2>
            <div className="space-y-8">
              {filteredReleases.length > 0 ? (
                filteredReleases.map((release) => (
                  <div key={release.id} className="relative">
                    {/* Release date indicator - Now on the right */}
                    <div className="absolute -right-2 top-6 transform translate-x-full hidden lg:block">
                      <div className="bg-gray-800 text-gray-300 px-3 py-1 rounded text-sm font-medium">
                        {new Date(release.releaseDate + "T00:00:00").toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                    </div>
                    {/* Mobile date display */}
                    <div className="text-gray-400 text-sm mb-2 lg:hidden">
                      {new Date(release.releaseDate + "T00:00:00").toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </div>
                    <ReleaseCard release={release} />
                  </div>
                ))
              ) : (
                <div className="text-gray-400 py-12 text-center">No releases found for this year.</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
