export interface Release {
  id: string;
  title: string;
  artist: string;
  coverUrl: string;
  releaseDate: string;
  tracks: Track[];
  downloadUrl: string;
  platforms: {
    bandcamp?: string;
    soundcloud?: string;
    spotify?: string;
    appleMusicUrl?: string;
    youtubeMusicUrl?: string;
    amazonMusicUrl?: string;
  };
}

export interface Track {
  id: string;
  title: string;
  youtubeVideoId: string;
}

export interface LivePerformance {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  date: string;
  description: string;
}
