# Database resources
module "database" {
  source = "../../modules/database"

  environment = var.environment
  tags        = var.tags
}

# Website resources
module "website" {
  source = "../../modules/website"

  website_bucket_name = var.website_bucket_name
  website_domain      = var.website_domain
  acm_certificate_arn = module.domains.acm_certificate_arn
  tags                = var.tags
}

# API resources
module "api" {
  source = "../../modules/api"

  environment             = var.environment
  lambda_source_dir       = var.lambda_source_dir
  releases_table_name     = module.database.releases_table_name
  performances_table_name = module.database.performances_table_name
  dynamodb_table_arns     = module.database.table_arns
  tags                    = var.tags
}

# Domain resources
module "domains" {
  source = "../../modules/domains"
  providers = {
    aws.us-east-1 = aws.us-east-1
  }

  domain_name              = var.domain_name
  website_domain           = var.website_domain
  cloudfront_domain_name   = module.website.cloudfront_domain
  cloudfront_hosted_zone_id = module.website.cloudfront_hosted_zone_id
  tags                     = var.tags
}
