output "website_url" {
  description = "Website URL"
  value       = module.website.website_url
}

output "cloudfront_domain" {
  description = "CloudFront distribution domain name"
  value       = module.website.cloudfront_domain
}

output "api_url" {
  description = "API Gateway URL"
  value       = module.api.api_url
}

output "s3_bucket_name" {
  description = "Name of the S3 bucket hosting the website"
  value       = module.website.s3_bucket_name
}

output "dynamodb_releases_table" {
  description = "Name of the DynamoDB table for releases"
  value       = module.database.releases_table_name
}

output "dynamodb_performances_table" {
  description = "Name of the DynamoDB table for performances"
  value       = module.database.performances_table_name
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = module.api.lambda_function_name
}

output "environment" {
  description = "Current environment"
  value       = var.environment
}
