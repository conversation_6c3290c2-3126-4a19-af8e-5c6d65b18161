variable "environment" {
  description = "Environment name (staging or prod)"
  type        = string
}

variable "lambda_source_dir" {
  description = "Directory containing the Lambda function source code"
  type        = string
}

variable "releases_table_name" {
  description = "Name of the DynamoDB table for releases"
  type        = string
}

variable "performances_table_name" {
  description = "Name of the DynamoDB table for performances"
  type        = string
}

variable "dynamodb_table_arns" {
  description = "ARNs of the DynamoDB tables that the Lambda function needs access to"
  type        = list(string)
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
}
