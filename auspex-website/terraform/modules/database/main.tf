# Database module - DynamoDB resources

# DynamoDB table for releases
resource "aws_dynamodb_table" "releases" {
  name             = "releases-${var.environment}"
  billing_mode     = "PAY_PER_REQUEST"
  hash_key         = "id"
  stream_enabled   = true
  stream_view_type = "NEW_AND_OLD_IMAGES"
  tags             = var.tags

  attribute {
    name = "id"
    type = "S"
  }
}

# DynamoDB table for performances
resource "aws_dynamodb_table" "performances" {
  name             = "performances-${var.environment}"
  billing_mode     = "PAY_PER_REQUEST"
  hash_key         = "id"
  stream_enabled   = true
  stream_view_type = "NEW_AND_OLD_IMAGES"
  tags             = var.tags

  attribute {
    name = "id"
    type = "S"
  }
}
