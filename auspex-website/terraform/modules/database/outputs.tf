output "releases_table_name" {
  description = "Name of the DynamoDB table for releases"
  value       = aws_dynamodb_table.releases.name
}

output "performances_table_name" {
  description = "Name of the DynamoDB table for performances"
  value       = aws_dynamodb_table.performances.name
}

output "releases_table_arn" {
  description = "ARN of the DynamoDB table for releases"
  value       = aws_dynamodb_table.releases.arn
}

output "performances_table_arn" {
  description = "ARN of the DynamoDB table for performances"
  value       = aws_dynamodb_table.performances.arn
}

output "table_arns" {
  description = "List of ARNs for all DynamoDB tables"
  value       = [aws_dynamodb_table.releases.arn, aws_dynamodb_table.performances.arn]
}
