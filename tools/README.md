# Auspex Records Label Tools


## [`convert_s3_codecs.py`](convert_s3_codecs.py)

- Compares the folders in the S3 pre-process bucket with those in the S3 releases
bucket and processes any that are missing.
- Converts the audio files in each directory into each of the codecs listed below.
- Copies any non-WAV files as-is (i.e. cover art) into the codec folders.
- Zips the codec folders individually.
- Uploads each collection of zips to its own folder in the S3 releases bucket.

Codecs used:
- MP3 320
- MP3 V0
- FLAC
- AAC
- Ogg Vorbis
- ALAC
- WAV
- AIFF

### Setup

- Install python3.12
- Install ffmpeg
- Install AWS CLI
- Run `aws configure` and follow instructions.
- Create a virtualenv and run `pip3 install -r requirements.txt`
- Create two S3 buckets for pre- and post-processed (released) albums.